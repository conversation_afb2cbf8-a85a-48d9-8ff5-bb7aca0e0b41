# 应用配置
APP_NAME=AntCode API
APP_VERSION=1.0.0
APP_DESCRIPTION=AntCode FastAPI Application
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:8080
CORS_ALLOW_CREDENTIALS=true

# JWT配置
JWT_SECRET_KEY=your-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=sqlite://./data/app.db
DATABASE_ECHO=false

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs
LOG_FILE=app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
BCRYPT_ROUNDS=12

# API配置
API_V1_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc
OPENAPI_URL=/openapi.json

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760
