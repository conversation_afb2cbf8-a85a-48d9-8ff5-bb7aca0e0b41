from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from src.api import api_router
from src.core.config import settings
from aerich import Command
from tortoise import Tortoise
from loguru import logger


async def lifespan(app: FastAPI):
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )
    
    async def init_db():
        command = Command(tortoise_config=settings.TORTOISE_ORM)
        try:
            await command.init_db(safe=True)
        except Exception as e:
            pass
        await command.init()
        try:
            await command.migrate()
        except AttributeError:
            logger.warning(f"Failed to migrate {command}")
            s
    yield
    
    await Tortoise.close_connections()


def create_app() -> FastAPI:
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description=settings.APP_DESCRIPTION,
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )
    
    app.include_router(api_router, prefix='/api')
    return app
