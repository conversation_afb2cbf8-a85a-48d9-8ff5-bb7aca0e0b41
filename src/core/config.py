import os
from pathlib import Path
from typing import List
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基本信息
    APP_TITLE: str = "AntCode API"
    PROJECT_NAME: str = "AntCode API"
    APP_DESCRIPTION: str = "AntCode API"

    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True

    # CORS配置
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # JWT配置
    JWT_SECRET_KEY: str = "auN4sWFbxtnlZywfYmOvLf7QhPrhgXCjOqjRYOpwcDk"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 项目路径配置
    PROJECT_ROOT: str = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Tortoise ORM配置
    TORTOISE_ORM: dict = {
        "connections": {
            "default": f"sqlite://{PROJECT_ROOT}/antcode.db"
        },
        "apps": {
            "models": {
                "models": ["src.models"],
                "default_connection": "default",
            },
        },
        "timezone": "Asia/Shanghai"
    }
settings = Settings()